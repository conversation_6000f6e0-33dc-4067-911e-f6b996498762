<template>
	<view class="register-container">
		<!-- 顶部图片区域 -->
		<view class="header-image">
			<image
				src="./image.png"
				mode="aspectFill"
				class="header-bg-image"
			></image>
		</view>

		<!-- 主要内容区域 -->
		<view class="main-content">
			<view class="register-form">
				<!-- 用户注册表单 -->
				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👤</text>
						<input
							v-model="ruleForm.yonghuzhanghao"
							class="form-input"
							type="text"
							placeholder="请输入用户账号"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👨</text>
						<input
							v-model="ruleForm.xingming"
							class="form-input"
							type="text"
							placeholder="请输入姓名"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							v-model="ruleForm.mima"
							class="form-input"
							type="password"
							placeholder="请输入密码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔐</text>
						<input
							v-model="ruleForm.mima2"
							class="form-input"
							type="password"
							placeholder="请确认密码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>
				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper picker-wrapper">
						<text class="input-icon">⚥</text>
						<picker @change="yonghuxingbieChange" :value="yonghuxingbieIndex" :range="yonghuxingbieOptions" class="form-picker">
							<view class="picker-text">{{ruleForm.xingbie || "请选择性别"}}</view>
						</picker>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🎂</text>
						<input
							v-model="ruleForm.nianling"
							class="form-input"
							type="number"
							placeholder="请输入年龄"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">📱</text>
						<input
							v-model="ruleForm.shouji"
							class="form-input"
							type="text"
							placeholder="请输入手机号码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🆔</text>
						<input
							v-model="ruleForm.shenfenzheng"
							class="form-input"
							type="text"
							placeholder="请输入身份证号"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yonghu'" class="form-item">
					<view class="input-wrapper upload-wrapper" @tap="yonghutouxiangTap">
						<text class="input-icon">📷</text>
						<view class="upload-content">
							<image v-if="ruleForm.touxiang" class="avatar-preview" :src="baseUrl+ruleForm.touxiang" mode="aspectFill"></image>
							<image v-else class="avatar-placeholder" src="../../static/gen/upload.png" mode="aspectFill"></image>
							<text class="upload-text">{{ruleForm.touxiang ? '更换头像' : '上传头像'}}</text>
						</view>
					</view>
				</view>
				<!-- 医生注册表单 -->
				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🏥</text>
						<input
							v-model="ruleForm.yishenggonghao"
							class="form-input"
							type="text"
							placeholder="请输入医生工号"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">👨‍⚕️</text>
						<input
							v-model="ruleForm.yishengxingming"
							class="form-input"
							type="text"
							placeholder="请输入医生姓名"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔒</text>
						<input
							v-model="ruleForm.mima"
							class="form-input"
							type="password"
							placeholder="请输入密码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🔐</text>
						<input
							v-model="ruleForm.mima2"
							class="form-input"
							type="password"
							placeholder="请确认密码"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper picker-wrapper">
						<text class="input-icon">⚥</text>
						<picker @change="yishengxingbieChange" :value="yishengxingbieIndex" :range="yishengxingbieOptions" class="form-picker">
							<view class="picker-text">{{ruleForm.xingbie || "请选择性别"}}</view>
						</picker>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper upload-wrapper" @tap="yishengzhaopianTap">
						<text class="input-icon">📷</text>
						<view class="upload-content">
							<image v-if="ruleForm.zhaopian" class="avatar-preview" :src="baseUrl+ruleForm.zhaopian" mode="aspectFill"></image>
							<image v-else class="avatar-placeholder" src="../../static/gen/upload.png" mode="aspectFill"></image>
							<text class="upload-text">{{ruleForm.zhaopian ? '更换照片' : '上传照片'}}</text>
						</view>
					</view>
				</view>
				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">📞</text>
						<input
							v-model="ruleForm.lianxifangshi"
							class="form-input"
							type="text"
							placeholder="请输入联系方式"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">🆔</text>
						<input
							v-model="ruleForm.shenfenzheng"
							class="form-input"
							type="text"
							placeholder="请输入身份证号"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<view v-if="tableName=='yisheng'" class="form-item">
					<view class="input-wrapper">
						<text class="input-icon">📧</text>
						<input
							v-model="ruleForm.youxiang"
							class="form-input"
							type="text"
							placeholder="请输入邮箱地址"
							placeholder-class="placeholder-style"
						/>
					</view>
				</view>

				<!-- 注册按钮 -->
				<button class="register-btn" @tap="register" type="primary">
					立即注册
				</button>

				<!-- 返回登录链接 -->
				<view class="login-link">
					<text class="login-text" @tap="goToLogin">
						已有账号？返回登录
					</text>
				</view>
			</view>
		</view>

		<!-- 底部装饰
		<view class="footer-decoration">
			<text class="footer-text">专业 · 贴心 · 安全</text>
		</view> -->
	</view>
</template>

<script>
    import multipleSelect from "@/components/momo-multipleSelect/momo-multipleSelect";
	export default {
		data() {
			return {
                yonghuxingbieOptions: [],
                yonghuxingbieIndex: 0,
                yishengxingbieOptions: [],
                yishengxingbieIndex: 0,
				ruleForm: {
                yonghuzhanghao: '',
                xingming: '',
                mima: '',
                xingbie: '',
                nianling: '',
                shouji: '',
                shenfenzheng: '',
                touxiang: '',
				money: 0,
                yishenggonghao: '',
                yishengxingming: '',
                mima: '',
                xingbie: '',
                zhaopian: '',
                lianxifangshi: '',
                shenfenzheng: '',
                youxiang: '',
				money: 0,
				},
				tableName:""
			}
		},
        components: {
            multipleSelect
        },
        computed: {
            baseUrl() {
                return this.$base.url;
            },
        },
		async onLoad() {
			let table = uni.getStorageSync("loginTable");
            this.tableName = table;

                        // 自定义下拉框值
			if(this.tableName=='yonghu'){
                this.yonghuxingbieOptions = "男,女".split(',');
				this.ruleForm.xingbie=this.yonghuxingbieOptions[0]
			}
                        // 自定义下拉框值
			if(this.tableName=='yisheng'){
                this.yishengxingbieOptions = "男,女".split(',');
				this.ruleForm.xingbie=this.yishengxingbieOptions[0]
			}
			
			this.styleChange()
		},
		methods: {

            // 下拉变化
            yonghuxingbieChange(e) {
                    this.yonghuxingbieIndex = e.target.value
                    this.ruleForm.xingbie = this.yonghuxingbieOptions[this.yonghuxingbieIndex]
            },
            yonghutouxiangTap() {
                let _this = this;
                this.$api.upload(function(res) {
                    _this.ruleForm.touxiang = 'upload/' + res.file;
					_this.$forceUpdate();
                });
            },
            // 下拉变化
            yishengxingbieChange(e) {
                    this.yishengxingbieIndex = e.target.value
                    this.ruleForm.xingbie = this.yishengxingbieOptions[this.yishengxingbieIndex]
            },
            yishengzhaopianTap() {
                let _this = this;
                this.$api.upload(function(res) {
                    _this.ruleForm.zhaopian = 'upload/' + res.file;
					_this.$forceUpdate();
                });
            },

            toggleTab(str) {
                this.$refs[str].show();
            },

			styleChange() {
				this.$nextTick(()=>{
					// document.querySelectorAll('.uni-input .uni-input-input').forEach(el=>{
					//   el.style.backgroundColor = this.registerFrom.content.input.backgroundColor
					// })
				})
			},
			// 获取uuid
			getUUID () {
				return new Date().getTime();
			},
			// 注册
			// 返回登录页面
			goToLogin() {
				this.$utils.jump('../login/login')
			},
			// 注册
			async register() {

				if((!this.ruleForm.yonghuzhanghao) && `yonghu` == this.tableName){
					this.$utils.msg(`用户账号不能为空`);
					return
				}
				if((!this.ruleForm.xingming) && `yonghu` == this.tableName){
					this.$utils.msg(`姓名不能为空`);
					return
				}
				if((!this.ruleForm.mima) && `yonghu` == this.tableName){
					this.$utils.msg(`密码不能为空`);
					return
				}
                if(`yonghu` == this.tableName && (this.ruleForm.mima!=this.ruleForm.mima2)){
                    this.$utils.msg(`两次密码输入不一致`);
                    return
                }
				if(`yonghu` == this.tableName && this.ruleForm.shouji&&(!this.$validate.isMobile(this.ruleForm.shouji))){
					this.$utils.msg(`手机应输入手机格式`);
					return
				}
				if(`yonghu` == this.tableName && this.ruleForm.shenfenzheng&&(!this.$validate.checkIdCard(this.ruleForm.shenfenzheng))){
					this.$utils.msg(`身份证应输入身份证格式`);
					return
				}
				if(`yonghu` == this.tableName && this.ruleForm.money&&(!this.$validate.isNumber(this.ruleForm.money))){
					this.$utils.msg(`余额应输入数字`);
					return
				}
				if((!this.ruleForm.yishenggonghao) && `yisheng` == this.tableName){
					this.$utils.msg(`医生工号不能为空`);
					return
				}
				if((!this.ruleForm.yishengxingming) && `yisheng` == this.tableName){
					this.$utils.msg(`医生姓名不能为空`);
					return
				}
				if((!this.ruleForm.mima) && `yisheng` == this.tableName){
					this.$utils.msg(`密码不能为空`);
					return
				}
                if(`yisheng` == this.tableName && (this.ruleForm.mima!=this.ruleForm.mima2)){
                    this.$utils.msg(`两次密码输入不一致`);
                    return
                }
				if(`yisheng` == this.tableName && this.ruleForm.lianxifangshi&&(!this.$validate.isMobile(this.ruleForm.lianxifangshi))){
					this.$utils.msg(`联系方式应输入手机格式`);
					return
				}
				if(`yisheng` == this.tableName && this.ruleForm.shenfenzheng&&(!this.$validate.checkIdCard(this.ruleForm.shenfenzheng))){
					this.$utils.msg(`身份证应输入身份证格式`);
					return
				}
				if(`yisheng` == this.tableName && this.ruleForm.youxiang&&(!this.$validate.isEmail(this.ruleForm.youxiang))){
					this.$utils.msg(`邮箱应输入邮件格式`);
					return
				}
				if(`yisheng` == this.tableName && this.ruleForm.money&&(!this.$validate.isNumber(this.ruleForm.money))){
					this.$utils.msg(`余额应输入数字`);
					return
				}
				await this.$api.register(`${this.tableName}`, this.ruleForm);
				this.$utils.msgBack('注册成功');;
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.register-container {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2E5984 100%);
		position: relative;
		overflow: hidden;
	}

	.header-image {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 40vh;
		z-index: 1;
		overflow: hidden;

		.header-bg-image {
			width: 100%;
			height: 80%;
			object-fit: cover;
		}

		/* 添加渐变遮罩，让图片与下方内容更好融合 */
		&::after {
			content: '';
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			height: 100rpx;
			background: linear-gradient(to bottom, transparent, rgba(74,144,226,0.3));
			pointer-events: none;
		}
	}

	.main-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: flex-start;
		align-items: center;
		padding: 40rpx 60rpx 120rpx;
		z-index: 2;
		position: relative;
		margin-top: 32vh;
		background: linear-gradient(135deg, #4A90E2 0%, #357ABD 50%, #2E5984 100%);
		overflow-y: auto;
		min-height: 62vh;
	}

	.register-form {
		width: 100%;
		max-width: 600rpx;

		.form-item {
			margin-bottom: 32rpx;

			.input-wrapper {
				position: relative;
				background: rgba(255,255,255,0.95);
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				padding: 0 30rpx;
				box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.1);
				backdrop-filter: blur(10rpx);
				border: 1rpx solid rgba(255,255,255,0.2);
				min-height: 88rpx;

				.input-icon {
					font-size: 32rpx;
					margin-right: 20rpx;
					color: #4A90E2;
				}

				.form-input {
					flex: 1;
					height: 88rpx;
					font-size: 32rpx;
					color: #333333;
					background: transparent;
					border: none;
				}

				.placeholder-style {
					color: #999999;
					font-size: 30rpx;
				}
			}

			.picker-wrapper {
				.form-picker {
					flex: 1;
					height: 88rpx;
					display: flex;
					align-items: center;

					.picker-text {
						font-size: 32rpx;
						color: #333333;
					}
				}
			}

			.upload-wrapper {
				padding: 20rpx 30rpx;
				min-height: 120rpx;

				.upload-content {
					flex: 1;
					display: flex;
					align-items: center;

					.avatar-preview, .avatar-placeholder {
						width: 80rpx;
						height: 80rpx;
						border-radius: 12rpx;
						margin-right: 20rpx;
					}

					.avatar-placeholder {
						background: #f0f0f0;
					}

					.upload-text {
						font-size: 30rpx;
						color: #666666;
					}
				}
			}
		}

		.register-btn {
			width: 100%;
			height: 96rpx;
			background: linear-gradient(135deg, #FF6B6B 0%, #FF5252 100%);
			border: none;
			border-radius: 16rpx;
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			margin: 60rpx 0 40rpx;
			box-shadow: 0 8rpx 24rpx rgba(255,107,107,0.3);
			transition: all 0.3s ease;

			&:active {
				transform: translateY(2rpx);
				box-shadow: 0 4rpx 12rpx rgba(255,107,107,0.3);
			}
		}

		.login-link {
			text-align: center;
			margin-top: 40rpx;

			.login-text {
				font-size: 28rpx;
				color: rgba(255,255,255,0.8);
				text-decoration: underline;
				text-underline-offset: 4rpx;
			}
		}
	}

	.footer-decoration {
		position: fixed;
		bottom: 40rpx;
		left: 0;
		right: 0;
		text-align: center;
		z-index: 3;

		.footer-text {
			font-size: 24rpx;
			color: rgba(255,255,255,0.6);
			letter-spacing: 4rpx;
		}
	}

	/* 响应式适配 */
	@media screen and (max-height: 1200rpx) {
		.header-image {
			height: 35vh;
		}

		.main-content {
			margin-top: 33vh;
			padding: 30rpx 50rpx 100rpx;
			min-height: 67vh;
		}
	}

	@media screen and (max-height: 1000rpx) {
		.header-image {
			height: 30vh;
		}

		.main-content {
			margin-top: 28vh;
			padding: 20rpx 40rpx 80rpx;
			min-height: 72vh;
		}
	}
</style>
