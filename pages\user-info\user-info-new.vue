<template>
<view class="page-container">
	<!-- 自定义导航栏 -->
	<view class="custom-navbar">
		<view class="navbar-content">
			<view class="navbar-left" @tap="goBack">
				<text class="iconfont icon-fanhui"></text>
			</view>
			<view class="navbar-title">个人信息</view>
			<view class="navbar-right"></view>
		</view>
	</view>
	
	<!-- 头像区域 -->
	<view class="avatar-section" v-if="tableName=='yonghu'" @tap="yonghutouxiangTap">
		<view class="avatar-container">
			<image class="avatar-image" v-if="ruleForm.touxiang" :src="baseUrl+ruleForm.touxiang" mode="aspectFill"></image>
			<image class="avatar-image" v-else src="../../static/gen/upload.png" mode="aspectFill"></image>
			<view class="avatar-edit-icon">
				<text class="iconfont icon-xiangji"></text>
			</view>
		</view>
		<text class="avatar-tip">点击更换头像</text>
	</view>
	
	<view class="avatar-section" v-if="tableName=='yisheng'" @tap="yishengzhaopianTap">
		<view class="avatar-container">
			<image class="avatar-image" v-if="ruleForm.zhaopian" :src="baseUrl+ruleForm.zhaopian" mode="aspectFill"></image>
			<image class="avatar-image" v-else src="../../static/gen/upload.png" mode="aspectFill"></image>
			<view class="avatar-edit-icon">
				<text class="iconfont icon-xiangji"></text>
			</view>
		</view>
		<text class="avatar-tip">点击更换照片</text>
	</view>

	<!-- 表单区域 -->
	<view class="form-container">
		<!-- 用户信息表单 -->
		<view v-if="tableName=='yonghu'" class="form-section">
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-yonghu"></text>
					<text>用户账号</text>
				</view>
				<input class="form-input disabled" disabled v-model="ruleForm.yonghuzhanghao" placeholder="用户账号" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-xingming"></text>
					<text>姓名</text>
				</view>
				<input class="form-input" v-model="ruleForm.xingming" placeholder="请输入姓名" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-xingbie"></text>
					<text>性别</text>
				</view>
				<picker class="form-picker" @change="yonghuxingbieChange" :value="yonghuxingbieIndex" :range="yonghuxingbieOptions">
					<view class="picker-content">
						<text class="picker-text">{{ruleForm.xingbie || "请选择性别"}}</text>
						<text class="iconfont icon-xiala"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-nianling"></text>
					<text>年龄</text>
				</view>
				<input class="form-input" v-model="ruleForm.nianling" placeholder="请输入年龄" type="number" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-shouji"></text>
					<text>手机</text>
				</view>
				<input class="form-input" v-model="ruleForm.shouji" placeholder="请输入手机号码" type="number" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-shenfenzheng"></text>
					<text>身份证</text>
				</view>
				<input class="form-input" v-model="ruleForm.shenfenzheng" placeholder="请输入身份证号码" />
			</view>
		</view>

		<!-- 医生信息表单 -->
		<view v-if="tableName=='yisheng'" class="form-section">
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-yisheng"></text>
					<text>医生工号</text>
				</view>
				<input class="form-input disabled" disabled v-model="ruleForm.yishenggonghao" placeholder="医生工号" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-xingming"></text>
					<text>医生姓名</text>
				</view>
				<input class="form-input" v-model="ruleForm.yishengxingming" placeholder="请输入医生姓名" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-xingbie"></text>
					<text>性别</text>
				</view>
				<picker class="form-picker" @change="yishengxingbieChange" :value="yishengxingbieIndex" :range="yishengxingbieOptions">
					<view class="picker-content">
						<text class="picker-text">{{ruleForm.xingbie || "请选择性别"}}</text>
						<text class="iconfont icon-xiala"></text>
					</view>
				</picker>
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-shouji"></text>
					<text>联系方式</text>
				</view>
				<input class="form-input" v-model="ruleForm.lianxifangshi" placeholder="请输入联系方式" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-shenfenzheng"></text>
					<text>身份证</text>
				</view>
				<input class="form-input" v-model="ruleForm.shenfenzheng" placeholder="请输入身份证号码" />
			</view>
			
			<view class="form-item">
				<view class="form-label">
					<text class="iconfont icon-youxiang"></text>
					<text>邮箱</text>
				</view>
				<input class="form-input" v-model="ruleForm.youxiang" placeholder="请输入邮箱地址" />
			</view>
		</view>
	</view>

	<!-- 按钮区域 -->
	<view class="button-container">
		<button class="save-btn" @tap="update()">
			<text class="iconfont icon-baocun"></text>
			<text>保存</text>
		</button>
		<button class="logout-btn" @tap="logout()">
			<text class="iconfont icon-tuichu"></text>
			<text>退出登录</text>
		</button>
	</view>
</view>
</template>

<script>
import xiaEditor from '@/components/xia-editor/xia-editor';
import multipleSelect from "@/components/momo-multipleSelect/momo-multipleSelect";

export default {
	data() {
		return {
			ruleForm: {},
			tableName: "",
			yonghuxingbieOptions: [],
			yonghuxingbieIndex: 0,
			yishengxingbieOptions: [],
			yishengxingbieIndex: 0,
		}
	},
	components: {
		multipleSelect,
		xiaEditor
	},
	computed: {
		baseUrl() {
			return this.$base.url;
		}
	},
	async onLoad() {
		let table = uni.getStorageSync("nowTable");
		this.tableName = table;
		this.getSession()
		
		// 自定义下拉框值
		if(this.tableName=='yonghu'){
			this.yonghuxingbieOptions = "男,女".split(',');
			this.yonghuxingbieOptions.forEach((item, index) => {
				if(item==this.ruleForm.xingbie) {
					this.yonghuxingbieIndex = index;
				}
			});
		}
		
		// 自定义下拉框值
		if(this.tableName=='yisheng'){
			this.yishengxingbieOptions = "男,女".split(',');
			this.yishengxingbieOptions.forEach((item, index) => {
				if(item==this.ruleForm.xingbie) {
					this.yishengxingbieIndex = index;
				}
			});
		}
		
		this.$forceUpdate()
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		
		async getSession(){
			let res = await this.$api.session(this.tableName)
			this.ruleForm = res.data;
			uni.setStorageSync('userSession',JSON.stringify(res.data))
		},
		
		// 下拉变化
		yonghuxingbieChange(e) {
			this.yonghuxingbieIndex = e.target.value
			this.ruleForm.xingbie = this.yonghuxingbieOptions[this.yonghuxingbieIndex]
		},
		
		yonghutouxiangTap() {
			let _this = this;
			this.$api.upload(function(res) {
				_this.ruleForm.touxiang = 'upload/' + res.file;
				_this.$forceUpdate();
				// 上传成功后自动保存到数据库
				_this.saveAvatar();
			});
		},
		
		// 保存头像到数据库
		async saveAvatar() {
			try {
				let table = uni.getStorageSync("nowTable");
				await this.$api.update(table, this.ruleForm);
				this.$utils.msg('头像更新成功');
				// 更新本地session数据
				this.getSession();
			} catch (error) {
				console.error('保存头像失败:', error);
				this.$utils.msg('头像保存失败，请重试');
			}
		},
		
		// 下拉变化
		yishengxingbieChange(e) {
			this.yishengxingbieIndex = e.target.value
			this.ruleForm.xingbie = this.yishengxingbieOptions[this.yishengxingbieIndex]
		},
		
		yishengzhaopianTap() {
			let _this = this;
			this.$api.upload(function(res) {
				_this.ruleForm.zhaopian = 'upload/' + res.file;
				_this.$forceUpdate();
				// 上传成功后自动保存到数据库
				_this.saveAvatar();
			});
		},

		logout() {
			uni.clearStorageSync()
			this.$utils.jump('../login/login');
		},
		
		// 保存更新
		async update() {
			// 验证逻辑保持不变
			if((!this.ruleForm.yonghuzhanghao) && `yonghu` == this.tableName){
				this.$utils.msg(`用户账号不能为空`);
				return
			}

			if((!this.ruleForm.xingming) && `yonghu` == this.tableName){
				this.$utils.msg(`姓名不能为空`);
				return
			}

			if(`yonghu` == this.tableName && this.ruleForm.shouji&&(!this.$validate.isMobile(this.ruleForm.shouji))){
				this.$utils.msg(`手机应输入手机格式`);
				return
			}

			if(`yonghu` == this.tableName && this.ruleForm.shenfenzheng&&(!this.$validate.checkIdCard(this.ruleForm.shenfenzheng))){
				this.$utils.msg(`身份证应输入身份证格式`);
				return
			}

			if((!this.ruleForm.yishenggonghao) && `yisheng` == this.tableName){
				this.$utils.msg(`医生工号不能为空`);
				return
			}

			if((!this.ruleForm.yishengxingming) && `yisheng` == this.tableName){
				this.$utils.msg(`医生姓名不能为空`);
				return
			}

			if(`yisheng` == this.tableName && this.ruleForm.lianxifangshi&&(!this.$validate.isMobile(this.ruleForm.lianxifangshi))){
				this.$utils.msg(`联系方式应输入手机格式`);
				return
			}

			if(`yisheng` == this.tableName && this.ruleForm.shenfenzheng&&(!this.$validate.checkIdCard(this.ruleForm.shenfenzheng))){
				this.$utils.msg(`身份证应输入身份证格式`);
				return
			}

			if(`yisheng` == this.tableName && this.ruleForm.youxiang&&(!this.$validate.isEmail(this.ruleForm.youxiang))){
				this.$utils.msg(`邮箱应输入邮件格式`);
				return
			}
			
			let table = uni.getStorageSync("nowTable");
			await this.$api.update(table, this.ruleForm);
			this.$utils.msgBack('修改成功');
			this.getSession()
		},
	}
}
</script>

<style lang="scss" scoped>
.page-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #E8F4FD 0%, #F0F8FF 100%);
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 2rpx 20rpx rgba(74, 144, 226, 0.3);
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 88rpx;
	padding: 0 32rpx;
}

.navbar-left, .navbar-right {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.navbar-left {
	.iconfont {
		font-size: 36rpx;
		color: #fff;
	}
}

.navbar-title {
	flex: 1;
	text-align: center;
	font-size: 36rpx;
	font-weight: 600;
	color: #fff;
	letter-spacing: 1rpx;
}

/* 头像区域 */
.avatar-section {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 140rpx 0 60rpx;
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	position: relative;

	&::after {
		content: '';
		position: absolute;
		bottom: 0;
		left: 0;
		right: 0;
		height: 60rpx;
		background: linear-gradient(135deg, #E8F4FD 0%, #F0F8FF 100%);
		border-radius: 60rpx 60rpx 0 0;
	}
}

.avatar-container {
	position: relative;
	margin-bottom: 20rpx;
}

.avatar-image {
	width: 160rpx;
	height: 160rpx;
	border-radius: 80rpx;
	border: 6rpx solid rgba(255, 255, 255, 0.9);
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.avatar-edit-icon {
	position: absolute;
	bottom: 8rpx;
	right: 8rpx;
	width: 48rpx;
	height: 48rpx;
	background: #4A90E2;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 3rpx solid #fff;
	box-shadow: 0 4rpx 12rpx rgba(74, 144, 226, 0.4);

	.iconfont {
		font-size: 24rpx;
		color: #fff;
	}
}

.avatar-tip {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.9);
	position: relative;
	z-index: 1;
}

/* 表单区域 */
.form-container {
	padding: 0 32rpx 40rpx;
	margin-top: -30rpx;
	position: relative;
	z-index: 1;
}

.form-section {
	background: #fff;
	border-radius: 24rpx;
	padding: 40rpx 32rpx;
	box-shadow: 0 8rpx 32rpx rgba(74, 144, 226, 0.08);
	border: 1rpx solid rgba(74, 144, 226, 0.1);
}

.form-item {
	margin-bottom: 40rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.form-label {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	.iconfont {
		font-size: 32rpx;
		color: #4A90E2;
		margin-right: 12rpx;
		width: 32rpx;
		text-align: center;
	}

	text {
		font-size: 30rpx;
		color: #333;
		font-weight: 500;
	}
}

.form-input {
	width: 100%;
	height: 88rpx;
	background: #F8F9FA;
	border: 2rpx solid #E9ECEF;
	border-radius: 16rpx;
	padding: 0 24rpx;
	font-size: 30rpx;
	color: #333;
	transition: all 0.3s ease;

	&:focus {
		border-color: #4A90E2;
		background: #fff;
		box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
	}

	&.disabled {
		background: #F5F5F5;
		color: #999;
	}
}

.form-picker {
	width: 100%;
	height: 88rpx;
	background: #F8F9FA;
	border: 2rpx solid #E9ECEF;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	transition: all 0.3s ease;

	&:active {
		border-color: #4A90E2;
		background: #fff;
		box-shadow: 0 0 0 6rpx rgba(74, 144, 226, 0.1);
	}
}

.picker-content {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 24rpx;
}

.picker-text {
	font-size: 30rpx;
	color: #333;
	flex: 1;
}

.picker-content .iconfont {
	font-size: 24rpx;
	color: #999;
}

/* 按钮区域 */
.button-container {
	padding: 40rpx 32rpx;
	display: flex;
	flex-direction: column;
	gap: 24rpx;
}

.save-btn, .logout-btn {
	width: 100%;
	height: 88rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	font-weight: 600;
	border: none;
	transition: all 0.3s ease;

	.iconfont {
		font-size: 32rpx;
		margin-right: 12rpx;
	}
}

.save-btn {
	background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
	color: #fff;
	box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 16rpx rgba(74, 144, 226, 0.4);
	}
}

.logout-btn {
	background: #F8F9FA;
	color: #666;
	border: 2rpx solid #E9ECEF;

	&:active {
		background: #E9ECEF;
		transform: translateY(2rpx);
	}
}
</style>
